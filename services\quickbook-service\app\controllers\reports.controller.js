import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import * as status from "../utils/status_code.utils.js";
import logger from "../../config/logger.config.js";
import { getLastMonthDateRange } from "../utils/date.utils.js";
import reportsService from "../services/reports.service.js";
import { 
  ReportsErrorHandler, 
  ReportsResponseHandler, 
  ReportsValidator
} from "../utils/reports.utils.js";
import { getReportConfig } from "../../config/reports.config.js";


const processReport = async (req, res, reportType) => {
  const config = getReportConfig(reportType);
  if (!config) {
    return ReportsResponseHandler.sendError(res, `Unsupported report type: ${reportType}`, status.STATUS_CODE_BAD_REQUEST);
  }

  const { quickBookAccesstoken, realmId } = req.body;
  let { startDate, endDate } = req.body;

  logger.info('Processing report request', {
    reportType,
    startDate,
    endDate,
    realmId,
  });

  try {
    // Validate required fields
    if (!ReportsValidator.validateRequiredFields(req, res, reportType, config.requiresDates)) {
      return; // Response already sent
    }

    // Auto-generate dates for P&L if missing
    if (reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && (!startDate || !endDate)) {
      const lastMonthRange = getLastMonthDateRange();
      startDate = startDate || lastMonthRange.startDate;
      endDate = endDate || lastMonthRange.endDate;

      logger.info('Auto-generated dates for P&L report', {
        autoStartDate: startDate,
        autoEndDate: endDate,
      });
    }

    // Create account object
    const mockQuickbookAccount = {
      realm_id: realmId,
      access_token: quickBookAccesstoken,
    };

    // Fetch report data
    const reportData = await reportsService.getReportDataFromQuickBooks(
      mockQuickbookAccount,
      startDate,
      endDate,
      reportType,
      quickBookAccesstoken
    );

    // Map data with error handling
    let mappedData;
    try {
      // Handle async mapping functions
      if (config.isAsync) {
        mappedData = await config.mapFunction(reportData, null, realmId, startDate, endDate);
      } else {
        mappedData = config.mapFunction(reportData, null, realmId, startDate, endDate);
      }
    } catch (mappingError) {
      logger.error("Mapping function failed", {
        error: mappingError.message,
        stack: mappingError.stack,
        reportType,
        realmId,
      });
      return ReportsResponseHandler.sendError(res, ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED, status.STATUS_CODE_INTERNAL_SERVER_ERROR);
    }

    // Validate mapped data
    if (!mappedData?.reportData) {
      logger.error('Failed to map report data', { reportType, realmId });
      return ReportsResponseHandler.sendError(res, ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED, status.STATUS_CODE_INTERNAL_SERVER_ERROR);
    }

    // Save data
    const savedData = await config.saveFunction(mappedData);

    // Validate save result
    if (!savedData?.reportId) {
      logger.error('Failed to save report data', { reportType });
      return ReportsResponseHandler.sendError(res, ERROR_MESSAGES.DATABASE.SAVE_FAILED, status.STATUS_CODE_INTERNAL_SERVER_ERROR);
    }

    // Send success response
    const responseData = {
      reportId: savedData.reportId,
      columnsCount: savedData.columnsCount,
      rowsCount: savedData.rowsCount,
      reportType,
      dateRange: { startDate, endDate },
      realmId,
      ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && {
        autoGeneratedDates: !req.body.startDate || !req.body.endDate,
      }),
    };

    // Add balance sheet specific data
    if (reportType === HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET && savedData.totals) {
      responseData.totals = savedData.totals;
      responseData.balanceCheck = {
        isBalanced: savedData.totals.isBalanced,
        totalAssets: savedData.totals.totalAssets,
        totalLiabilitiesAndEquity: savedData.totals.totalLiabilitiesAndEquity,
      };
    }

    return ReportsResponseHandler.sendSuccess(res, config.successMessage, responseData);
  } catch (error) {
    return ReportsErrorHandler.handleApiError(error, res, reportType, realmId, 'processing');
  }
};

/**
 * Streamlined report controllers using configuration
 */
export const fetchTrialBalance = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE);
export const fetchProfitLoss = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS);
export const fetchBalanceSheet = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET);
export const fetchCashFlow = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW);





/**
 * Generic report retrieval function
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} reportType - Type of report
 * @param {Function} serviceFunction - Service function to call
 * @returns {Promise<Object>} HTTP response
 */

const getReportsByRealmId = async (req, res, reportType, serviceFunction) => {
  try {
    const { realmId } = req.params;

    // Validate realmId
    if (!realmId) {
      return ReportsErrorHandler.handleValidationError(res, 'realmId', reportType);
    }

    const reports = await serviceFunction(realmId, {
      order: HARDCODED_STRINGS.DB_ORDER.CREATED_AT_DESC,
      limit: parseInt(req.query.limit) || 10,
      offset: parseInt(req.query.offset) || 0,
    });

    return ReportsResponseHandler.sendSuccess(res, `${reportType} reports retrieved successfully`, {
      reports,
      realmId,
      count: reports.length,
    });
  } catch (error) {
    return ReportsErrorHandler.handleApiError(error, res, reportType, req.params?.realmId, 'retrieval');
  }
};

/**
 * Report retrieval controllers using generic function
 */
export const getTrialBalanceReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE_DISPLAY,
    reportsService.getTrialBalanceReportsByRealmId
  );

export const getProfitLossReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.PROFIT_LOSS_DISPLAY,
    reportsService.getProfitLossReportsByRealmId
  );

export const getBalanceSheetReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET_DISPLAY,
    reportsService.getBalanceSheetReportsByRealmId
  );

export const getCashFlowReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW_DISPLAY,
    reportsService.getCashFlowReportsByRealmId
  );
