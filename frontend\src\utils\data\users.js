import { validationRules } from "@/utils/methods/validation";

export const usersData = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    tenant: "Acme Corporation",
    lastLogin: "2024-01-20",
    createdAt: "2024-01-15",
    phone: "******-0101",
    department: "IT",
    position: "System Administrator",
    avatar: null,
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    tenant: "TechStart Inc",
    lastLogin: "2024-01-19",
    createdAt: "2024-02-20",
    phone: "******-0102",
    department: "Marketing",
    position: "Marketing Specialist",
    avatar: null,
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Manager",
    status: "Inactive",
    tenant: "Global Solutions",
    lastLogin: "2024-01-10",
    createdAt: "2024-01-10",
    phone: "******-0103",
    department: "Operations",
    position: "Operations Manager",
    avatar: null,
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    tenant: "Acme Corporation",
    lastLogin: "2024-01-21",
    createdAt: "2024-01-18",
    phone: "******-0104",
    department: "Finance",
    position: "Financial Analyst",
    avatar: null,
  },
  {
    id: 5,
    name: "Mike Davis",
    email: "<EMAIL>",
    role: "Super Admin",
    status: "Active",
    tenant: "TechStart Inc",
    lastLogin: "2024-01-22",
    createdAt: "2024-01-12",
    phone: "******-0105",
    department: "IT",
    position: "CTO",
    avatar: null,
  },
];

export const userFields = [
  {
    name: "name",
    label: "Full Name",
    type: "text",
    placeholder: "Enter full name",
    validation: { required: "Full name is required" },
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    placeholder: "<EMAIL>",
    validation: validationRules.email,
  },
  {
    name: "phone",
    label: "Phone",
    type: "tel",
    placeholder: "******-XXXX",
    validation: { required: "Phone number is required" },
  },
  {
    name: "role",
    label: "Role",
    type: "select",
    options: [
      { label: "Super Admin", value: "Super Admin" },
      { label: "Admin", value: "Admin" },
      { label: "Manager", value: "Manager" },
      { label: "User", value: "User" },
      { label: "Viewer", value: "Viewer" },
    ],
    validation: { required: "Role is required" },
  },
  {
    name: "tenant",
    label: "Tenant",
    type: "select",
    options: [
      { label: "Acme Corporation", value: "Acme Corporation" },
      { label: "TechStart Inc", value: "TechStart Inc" },
      { label: "Global Solutions", value: "Global Solutions" },
    ],
    validation: { required: "Tenant is required" },
  },
  {
    name: "department",
    label: "Department",
    type: "select",
    options: [
      { label: "IT", value: "IT" },
      { label: "Finance", value: "Finance" },
      { label: "Marketing", value: "Marketing" },
      { label: "Operations", value: "Operations" },
      { label: "HR", value: "HR" },
    ],
    validation: { required: "Department is required" },
  },
  {
    name: "position",
    label: "Position",
    type: "text",
    placeholder: "Enter job position",
    validation: { required: "Position is required" },
  },
  {
    name: "status",
    label: "Status",
    type: "select",
    options: [
      { label: "Active", value: "Active" },
      { label: "Inactive", value: "Inactive" },
    ],
    validation: { required: "Status is required" },
  },
];

export const initialValues = {
  name: "",
  email: "",
  phone: "",
  role: "Super Admin",
  tenant: "Acme Corporation",
  department: "IT",
  position: "",
  status: "Active",
};
