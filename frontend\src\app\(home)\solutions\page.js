"use client";

import HomeNavbar from "@/components/common/HomeNavbar";

export default function SolutionsPage() {
  return (
    <div className="min-h-screen bg-[#012F44]">
      <HomeNavbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 pt-24">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-100 sm:text-5xl">
            Solutions
          </h1>
          <p className="mt-4 text-xl text-white">
            Discover our comprehensive suite of solutions designed for modern accounting practices.
          </p>
        </div>
        
        <div className="mt-12 bg-gray-50 rounded-lg p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Solutions</h2>
            <p className="text-lg text-gray-700">
              This page will showcase our various solutions including bookkeeping automation, 
              client management tools, financial reporting, and integration capabilities.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
