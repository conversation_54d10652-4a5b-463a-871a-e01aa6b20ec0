{"name": "cpa-dashboard-auth-service", "version": "1.0.0", "type": "module", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false"}, "description": "CPA Dashboard Auth Service - Authentication and Authorization microservice", "author": "Mobio Solutions", "license": "ISC", "keywords": ["cpa", "dashboard", "auth", "authentication", "authorization", "jwt", "microservice", "nodejs", "express", "sequelize"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "aws-sdk": "^2.1692.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.2", "redis": "^4.7.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "speakeasy": "^2.0.0", "uuid": "^11.1.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"eslint": "^9.34.0", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.3.0", "jest": "^29.7.0", "supertest": "^7.0.0"}}