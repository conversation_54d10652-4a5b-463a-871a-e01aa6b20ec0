// Apps Manager Constants
export const BUTTON_LINK_TEXT = {
  newConnect: 'Connect',
  cancel: 'Cancel',
  addConnections: 'Add Connections',
  retry: 'Retry'
};

// Page Titles Constants
export const PAGE_TITLES = {
  addNewConnection: 'Add New Connection'
};

// Admin Role Constant
export const ADMIN_ROLE = 'admin';

// Messages Constants
export const MESSAGES = {
  SAGE_ORGANIZATION_ERROR: 'Organization ID is required for Sage connection',
  SAGE_CONNECT_SUCCESS: 'Sage connected successfully!',
  SAGE_CONNECT_ERROR: 'Failed to connect Sage',
  NETSUITE_ORGANIZATION_ERROR: 'Organization ID is required for NetSuite connection',
  NETSUITE_CONNECT_SUCCESS: 'NetSuite connected successfully!',
  NETSUITE_CONNECT_ERROR: 'Failed to connect NetSuite'
};

// User Form Name Keys
export const USER_FORM_NAME_KEYS = {
  company_name: 'company_name',
  email: 'email',
  sageUserID: 'sageUserID',
  sageUserPassword: 'sageUserPassword',
  sageSenderID: 'sageSenderID',
  sageSenderPassword: 'sageSenderPassword',
  sageCompanyID: 'sageCompanyID'
};

// User Label Text
export const USER_LABEL_TEXT = {
  company_name: 'Company Name',
  email: 'Email',
  sageUserID: 'Sage User ID',
  sageUserPassword: 'Sage User Password',
  sageSenderID: 'Sage Sender ID',
  sageSenderPassword: 'Sage Sender Password',
  sageCompanyID: 'Sage Company ID'
};

// User Form Placeholders
export const USER_FORM_PLACEHOLDERS = {
  company_name: 'Enter company name',
  email: 'Enter email address',
  sageUserID: 'Enter Sage User ID',
  sageUserPassword: 'Enter Sage User Password',
  sageSenderID: 'Enter Sage Sender ID',
  sageSenderPassword: 'Enter Sage Sender Password',
  sageCompanyID: 'Enter Sage Company ID'
};

// Organization Form Placeholders
export const ORGANIZATION_FORM_PLACEHOLDERS = {
  pleaseSelectOrganization: 'Please select an organization'
};
