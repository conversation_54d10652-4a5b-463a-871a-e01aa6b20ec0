import HomeNavbar from "./HomeNavbar";

export default function HomeLayout({ children, showPartnerLogos = false }) {
  return (
    <div className="min-h-screen relative">
      {/* Background with gradient and grid pattern */}
      <div
        className="absolute inset-0"
        style={{
          background: `
            linear-gradient(135deg, #0f766e 0%, #134e4a 50%, #065f46 100%),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: "100% 100%, 50px 50px, 50px 50px",
        }}
      ></div>

      {/* Background image overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
        style={{
          backgroundImage: "url('/home/<USER>')",
        }}
      ></div>

      {/* Content */}
      <div className="relative z-10">
        <HomeNavbar />

        {/* Main Content */}
        <div className="pt-20 px-4 sm:px-6 lg:px-8 pb-20">{children}</div>

        {/* Partner Logos Section - Only show on home page */}
        {showPartnerLogos && (
          <div className="absolute bottom-0 left-0 right-0 bg-green-800 bg-opacity-90 py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-center space-x-8 sm:space-x-12 lg:space-x-16 opacity-70">
                <div className="text-white text-sm font-medium">Spherule</div>
                <div className="text-white text-sm font-medium">
                  SAMSUNG Pay
                </div>
                <div className="text-white text-sm font-medium">VISA</div>
                <div className="text-white text-sm font-medium">amazon pay</div>
                <div className="text-white text-sm font-medium">PayPal</div>
                <div className="text-white text-sm font-medium">Alipay</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
