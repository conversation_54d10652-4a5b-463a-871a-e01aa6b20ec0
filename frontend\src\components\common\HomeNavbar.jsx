"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/methods/cn";
import { ROUTES } from "@/utils/constants/routes";

const HomeNavbar = () => {
  const pathname = usePathname();

  const navigationItems = [
    {
      name: "Home",
      href: ROUTES.HOME,
    },
    {
      name: "About",
      href: ROUTES.ABOUT,
    },
    {
      name: "Contact Us",
      href: ROUTES.CONTACT,
    },
    {
      name: "Solutions",
      href: ROUTES.SOLUTIONS,
    },
    {
      name: "Dashboard",
      href: ROUTES.HOME_DASHBOARD,
    },
  ];

  return (
    <nav className="bg-transparent absolute top-0 left-0 right-0 z-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center">
              <img src="/home/<USER>" alt="Perfino" className="h-8 w-auto" />
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="flex items-center space-x-8">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "text-white hover:text-green-400 transition-colors duration-200 font-medium",
                      isActive && "text-green-400"
                    )}
                  >
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Sign In Button */}
          <div className="flex items-center">
            <Link href="/login">
              <Button
                variant="outline"
                size="sm"
                className="border-white text-white hover:bg-white hover:text-gray-900 bg-transparent rounded-full px-6"
              >
                Sign In
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="bg-transparent inline-flex items-center justify-center p-2 rounded-md text-white hover:text-green-400 focus:outline-none"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Hamburger icon */}
              <svg
                className="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="md:hidden" id="mobile-menu">
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-black bg-opacity-90">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "block px-3 py-2 rounded-md text-base font-medium transition-all duration-200",
                  isActive
                    ? "text-green-400"
                    : "text-white hover:text-green-400"
                )}
              >
                {item.name}
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default HomeNavbar;
