"use client";

import HomeLayout from "@/components/common/HomeLayout";

export default function HomeDashboardPage() {
  return (
    <HomeLayout>
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-100 sm:text-5xl">
            Dashboard
          </h1>
          <p className="mt-4 text-xl text-white">
            Access your comprehensive dashboard for managing your financial
            operations.
          </p>
        </div>

        <div className="mt-12 bg-gray-50 rounded-lg p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Dashboard Overview
            </h2>
            <p className="text-lg text-gray-700">
              This page will contain the main dashboard interface with
              analytics, performance metrics, and financial intelligence tools.
            </p>
          </div>
        </div>
      </div>
    </HomeLayout>
  );
}
