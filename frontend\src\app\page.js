"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { Loader } from "@/components/ui/loading";
import { ROLE_CONSTANTS } from "@/utils/constants";
import { ROUTES } from "@/utils/constants/routes";
import HomeNavbar from "@/components/common/HomeNavbar";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, user, loading } = useSelector((state) => state.auth);

  useEffect(() => {
    if (!loading && isAuthenticated) {
      // Only redirect authenticated users to their respective dashboards
      if (user?.role?.description === ROLE_CONSTANTS.ROLE_TYPES.SUPER_ADMIN) {
        router.push(ROUTES.LISTING);
      } else {
        router.push(ROUTES.DASHBOARD);
      }
    }
  }, [isAuthenticated, user, loading, router]);

  // Show loader while checking authentication
  if (loading) {
    return <Loader text="Checking authentication..." />;
  }

  // If authenticated, show loader while redirecting
  if (isAuthenticated) {
    return <Loader text="Redirecting to dashboard..." />;
  }

  // Show home page for unauthenticated users
  return (
    <div className="min-h-screen bg-white">
      <HomeNavbar />

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Professional</span>{" "}
                  <span className="block text-[#6366F1] xl:inline">
                    CPA Dashboard
                  </span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  Comprehensive accounting management solution for professional
                  CPAs. Streamline your practice with our advanced dashboard for
                  bookkeeping, client management, and financial reporting.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link href="/login">
                      <Button size="lg" className="w-full">
                        Get Started
                      </Button>
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link href="/about">
                      <Button variant="outline" size="lg" className="w-full">
                        Learn More
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-r from-[#6366F1] to-[#8B5CF6] sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="text-white text-center">
              <div className="text-6xl mb-4">📊</div>
              <h3 className="text-2xl font-semibold">Dashboard Analytics</h3>
              <p className="text-lg opacity-90">
                Real-time insights for your practice
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-[#6366F1] font-semibold tracking-wide uppercase">
              Features
            </h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need to manage your practice
            </p>
          </div>

          <div className="mt-10">
            <div className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-[#6366F1] text-white">
                  📈
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">
                  Dashboard Analytics
                </p>
                <p className="mt-2 ml-16 text-base text-gray-500">
                  Get real-time insights into your practice performance with
                  comprehensive analytics.
                </p>
              </div>

              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-[#6366F1] text-white">
                  👥
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">
                  Client Management
                </p>
                <p className="mt-2 ml-16 text-base text-gray-500">
                  Efficiently manage all your clients and their financial data
                  in one place.
                </p>
              </div>

              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-[#6366F1] text-white">
                  📚
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">
                  Bookkeeping
                </p>
                <p className="mt-2 ml-16 text-base text-gray-500">
                  Streamlined bookkeeping processes with automated workflows and
                  reporting.
                </p>
              </div>

              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-[#6366F1] text-white">
                  🔗
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">
                  Integrations
                </p>
                <p className="mt-2 ml-16 text-base text-gray-500">
                  Connect with popular accounting software and tools for
                  seamless workflows.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
