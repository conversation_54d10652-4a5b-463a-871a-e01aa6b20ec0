"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { Loader } from "@/components/ui/loading";
import { ROLE_CONSTANTS } from "@/utils/constants";
import { ROUTES } from "@/utils/constants/routes";
import HomeNavbar from "@/components/common/HomeNavbar";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, user, loading } = useSelector((state) => state.auth);

  useEffect(() => {
    if (!loading && isAuthenticated) {
      // Only redirect authenticated users to their respective dashboards
      if (user?.role?.description === ROLE_CONSTANTS.ROLE_TYPES.SUPER_ADMIN) {
        router.push(ROUTES.LISTING);
      } else {
        router.push(ROUTES.DASHBOARD);
      }
    }
  }, [isAuthenticated, user, loading, router]);

  // Show loader while checking authentication
  if (loading) {
    return <Loader text="Checking authentication..." />;
  }

  // If authenticated, show loader while redirecting
  if (isAuthenticated) {
    return <Loader text="Redirecting to dashboard..." />;
  }

  // Show home page for unauthenticated users
  return (
    <div className="min-h-screen relative">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/home/<USER>')",
        }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        <HomeNavbar />

        {/* Hero Section */}
        <div className="flex flex-col justify-center min-h-[calc(100vh-80px)] px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto w-full">
            <div className="max-w-3xl">
              {/* Trust Badge */}
              <div className="mb-8">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-orange-100 text-orange-800 border border-orange-200">
                  🔥 100% TRUSTED PLATFORM
                </span>
              </div>

              {/* Main Heading */}
              <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
                AI Performance
                <br />
                Insights Meet
                <br />
                <span className="text-green-400">Financial</span>
                <br />
                <span className="text-green-400">Intelligence</span>
              </h1>

              {/* Subtitle */}
              <p className="text-xl sm:text-2xl text-gray-200 mb-8 max-w-2xl">
                Sleek Analytics, Trustworthy Automation, And Real-Time
                <br />
                Forecasting For Modern Finance Teams.
              </p>

              {/* CTA Button */}
              <div className="mb-16">
                <Link href="/login">
                  <Button
                    size="lg"
                    className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 text-lg font-semibold rounded-full inline-flex items-center gap-2"
                  >
                    Open Account
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Partner Logos Section */}
        <div className="absolute bottom-0 left-0 right-0 bg-green-800 bg-opacity-90 py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-center space-x-8 sm:space-x-12 lg:space-x-16 opacity-70">
              {/* Partner logos would go here - using text placeholders for now */}
              <div className="text-white text-sm font-medium">Spherule</div>
              <div className="text-white text-sm font-medium">SAMSUNG Pay</div>
              <div className="text-white text-sm font-medium">VISA</div>
              <div className="text-white text-sm font-medium">amazon pay</div>
              <div className="text-white text-sm font-medium">PayPal</div>
              <div className="text-white text-sm font-medium">Alipay</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
