{"name": "cpa-dashboard-tenant-service", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "migrate:status": "npx sequelize-cli db:migrate:status", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo", "seed:undo:all": "npx sequelize-cli db:seed:undo:all", "db:reset": "npm run migrate:undo:all && npm run migrate && npm run seed", "db:setup": "npm run migrate && npm run seed", "db:test": "node test-connection.js", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false"}, "description": "CPA Dashboard Tenant Service - Backend API for tenant management", "author": "Mobio Solutions", "license": "ISC", "keywords": ["cpa", "dashboard", "tenant", "api", "nodejs", "express", "sequelize"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@bull-board/api": "^6.9.1", "@bull-board/express": "^6.9.1", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.49.1", "amqplib": "^0.10.7", "aws-sdk": "^2.1692.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "bullmq": "^5.49.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "fast-csv": "^5.0.2", "helmet": "^8.0.0", "http-proxy-middleware": "^3.0.5", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mongoose": "^8.3.4", "morgan": "^1.10.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "pdfkit": "^0.17.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.2", "redis": "^4.7.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "speakeasy": "^2.0.0", "uuid": "^11.1.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.35.0", "eslint": "^9.35.0", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.15.0", "jest": "^29.7.0", "supertest": "^7.0.0"}}